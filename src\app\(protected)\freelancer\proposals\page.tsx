"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { Job, JobProposal } from "@/types/job";
import { jobService } from "@/api/jobService";
import { formatDistanceToNow } from "date-fns";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";
import { Briefcase, Search } from "lucide-react";
import { toast } from "react-hot-toast";

type JobProposalWithJob = JobProposal & {
  job?: Job;
};

type StatusFilter = "" | "PENDING" | "ACCEPTED" | "REJECTED";

const MyProposalsPage = () => {
  const auth = useAuth();
  const isAuthenticated = auth.isAuthenticated;
  const user = auth.user;
  const authLoading = auth.loading;
  const router = useRouter();

  const [proposals, setProposals] = useState<JobProposalWithJob[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<
    JobProposalWithJob[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("");

  useEffect(() => {
    if (
      !authLoading &&
      (!isAuthenticated || user?.attributes?.["custom:role"] !== "FREELANCER")
    ) {
      router.push("/login");
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchProposals = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!user?.username) {
        throw new Error("User not authenticated");
      }
      const data = await jobService.listMyProposals(user.username);
      setProposals(data);
      setFilteredProposals(data);
    } catch (err) {
      console.error("Error fetching proposals:", err);
      toast.error("Failed to load proposals. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    const filtered = proposals.filter((proposal) => {
      const matchesSearch =
        !searchTerm ||
        proposal.job?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proposal.job?.description
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      const matchesStatus = !statusFilter || proposal.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    setFilteredProposals(filtered);
  }, [proposals, searchTerm, statusFilter]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.["custom:role"] === "FREELANCER") {
      fetchProposals();
    }
  }, [isAuthenticated, user, fetchProposals]);

  // Search and filter functionality will be implemented in a future update
  // const filteredApplications = applications.filter((app) => {
  //   const matchesSearch = searchTerm === '' ||
  //     app.job?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
  //     app.job?.description?.toLowerCase().includes(searchTerm.toLowerCase());

  //   const matchesStatus = statusFilter === 'ALL' || app.status === statusFilter;
  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  const breadcrumbs = [
    { label: "Home", href: "/" },
    { label: "Freelancer Dashboard", href: "/freelancer/dashboard" },
    { label: "My Proposals", href: "/freelancer/proposals" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">My Proposals</h1>
          <p className="text-muted-foreground">
            Track the status of your job proposals
          </p>
        </div>
        <Button asChild className="w-full sm:w-auto">
          <Link
            href="/freelancer/jobs"
            className="flex items-center justify-center"
          >
            <Briefcase className="mr-2 h-4 w-4" />
            Find Jobs
          </Link>
        </Button>
      </div>

      <Card className="w-full">
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>My Proposals</CardTitle>
              <CardDescription>
                View and manage your job proposals
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2 w-full md:w-auto">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search proposals..."
                  className="w-full bg-background pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value as StatusFilter);
                }}
                options={[
                  { value: "", label: "All Status" },
                  { value: "PENDING", label: "Pending" },
                  { value: "ACCEPTED", label: "Accepted" },
                  { value: "REJECTED", label: "Rejected" },
                ]}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : filteredProposals.length === 0 ? (
            <div className="text-center py-12">
              <Briefcase className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No proposals
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                You haven&#39;t submitted any proposals yet.
              </p>
              <div className="mt-6">
                <Link href="/freelancer/jobs">
                  <Button>Browse Jobs</Button>
                </Link>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProposals.map((proposal) => (
                <Card key={proposal.id} className="overflow-hidden">
                  <div className="p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="space-y-1">
                        <div className="flex flex-wrap items-center gap-2">
                          <h3 className="text-lg font-medium">
                            {proposal.job?.title || "Job Title Not Available"}
                          </h3>
                          <div className="flex items-center gap-2">
                            {proposal.status === "ACCEPTED" && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Accepted
                              </span>
                            )}
                            {proposal.status === "REJECTED" && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Rejected
                              </span>
                            )}
                            {proposal.status === "PENDING" && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Pending
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Proposed ${proposal.bidAmount.toLocaleString()} •
                          Applied{" "}
                          {formatDistanceToNow(new Date(proposal.createdAt), {
                            addSuffix: true,
                          })}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/freelancer/jobs/${proposal.jobId}`}>
                            View Job
                          </Link>
                        </Button>
                      </div>
                    </div>
                    {proposal.coverLetter && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-1">
                          Cover Letter
                        </h4>
                        <p className="text-sm text-muted-foreground whitespace-pre-line">
                          {proposal.coverLetter}
                        </p>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MyProposalsPage;
