{"name": "myvillage-freelance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "amplify:pull:dev": "amplify pull --appId d2ikbshujrbi86 --envName dev"}, "dependencies": {"@apollo/client": "^3.13.9", "@hookform/resolvers": "^5.2.1", "aws-amplify": "^6.15.5", "clsx": "^2.1.1", "date-fns": "^4.1.0", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "lucide-react": "^0.540.0", "next": "15.4.7", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.6.0", "tailwind-merge": "^3.3.1", "yup": "^1.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.33.0", "@next/eslint-plugin-next": "^15.4.7", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "eslint": "^9.33.0", "eslint-config-next": "15.4.7", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}