"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { AuthLayout } from "@/components/layouts";
import { Form, FormField, Input, Button, Loading, RadioGroup, Icon } from "@/components/ui";
import { UserRole } from "@/types/enums";

interface SignUpFormData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  [key: string]: string | number | boolean | undefined;
}

export default function SignupPage() {
  const { signUp, confirmSignUp, isAuthenticated, user, loading, error: authError } = useAuth();
  const router = useRouter();
  
  const [step, setStep] = useState<"form" | "confirm">("form");
  const [formData, setFormData] = useState<SignUpFormData>({
    email: "",
    password: "",
    name: "",
    role: UserRole.CLIENT
  });
  const [code, setCode] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user) {
      const userRole = (user.attributes?.['custom:role'] as UserRole) || UserRole.CLIENT;
      const redirectPath = userRole === UserRole.CLIENT ? '/client/dashboard' : '/freelancer/dashboard';
      router.replace(redirectPath);
    }
  }, [isAuthenticated, user, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRoleChange = (role: UserRole) => {
    setFormData(prev => ({
      ...prev,
      role
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      await signUp({
        username: formData.email,
        password: formData.password,
        attributes: {
          email: formData.email,
          name: formData.name,
          'custom:role': formData.role
        }
      });
      
      setStep("confirm");
      setMessage("Check your email for the confirmation code.");
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : "Sign up failed. Please try again.";
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmation = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      await confirmSignUp(formData.email, code);
      setMessage("Account confirmed! Redirecting to login...");
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : "Confirmation failed. Please try again.";
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading || (isAuthenticated && !isSubmitting)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin text-gray-900" />
      </div>
    );
  }

  const roleOptions = [
    { value: UserRole.CLIENT, label: "Client", description: "I want to hire freelancers" },
    { value: UserRole.FREELANCER, label: "Freelancer", description: "I want to offer my services" },
  ];

  return (
    <AuthLayout
      title="Create Account"
      subtitle="Join MyVillage and start your freelance journey"
    >
      {step === "form" ? (
        <Form onSubmit={handleSubmit}>
          <FormField
            label="Full Name"
            required
            error={error && error.includes('name') ? error : undefined}
          >
            <Input
              name="name"
              type="text"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter your full name"
              required
              error={!!(error || authError)}
            />
          </FormField>

          <FormField
            label="Email"
            required
            error={error && error.includes('email') ? error : undefined}
          >
            <Input
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              required
              error={!!(error || authError)}
            />
          </FormField>

          <FormField
            label="Password"
            required
            error={error && error.includes('password') ? error : undefined}
            hint="Password must be at least 8 characters long"
          >
            <Input
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Create a password"
              required
              minLength={8}
              error={!!(error || authError)}
            />
          </FormField>

          <FormField
            label="I am a:"
            required
            error={error && error.includes('role') ? error : undefined}
          >
            <RadioGroup
              name="role"
              options={roleOptions}
              value={formData.role}
              onChange={(value) => handleRoleChange(value as UserRole)}
              orientation="vertical"
              error={!!(error || authError)}
            />
          </FormField>

          {(error || authError) && (
            <div className="p-3 bg-destructive/10 text-destructive text-sm rounded-md">
              {error || authError}
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loading size="sm" className="mr-2" />
                Creating Account...
              </>
            ) : (
              'Create Account'
            )}
          </Button>

          <div className="text-center text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link href="/login" className="text-primary hover:underline font-medium">
              Sign in
            </Link>
          </div>
        </Form>
      ) : (
        <Form onSubmit={handleConfirmation}>
          <div className="text-center mb-6">
            <p className="text-sm text-muted-foreground">
              We&apos;ve sent a verification code to{' '}
              <span className="font-medium text-foreground">{formData.email}</span>.
              Please enter it below to verify your email address.
            </p>
          </div>

          <FormField
            label="Verification Code"
            required
            error={error || undefined}
          >
            <Input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="Enter verification code"
              required
              error={!!error}
            />
          </FormField>

          {message && (
            <div className="p-3 bg-sage-100 text-sage-800 text-sm rounded-md">
              {message}
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loading size="sm" className="mr-2" />
                Verifying...
              </>
            ) : (
              'Verify Email'
            )}
          </Button>

          <div className="text-center text-sm text-muted-foreground">
            Didn&apos;t receive a code?{' '}
            <Button
              type="button"
              variant="link"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="p-0 h-auto text-primary"
            >
              Resend code
            </Button>
          </div>
        </Form>
      )}
    </AuthLayout>
  );
}

