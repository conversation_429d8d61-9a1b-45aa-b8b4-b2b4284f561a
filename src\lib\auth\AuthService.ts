import { 
  signIn, 
  signOut, 
  signUp, 
  confirmSignUp, 
  resetPassword, 
  confirmResetPassword, 
  getCurrentUser, 
  fetchAuthSession,
} from 'aws-amplify/auth';
import { BehaviorSubject } from 'rxjs';
import type { AuthUser } from '@/types/user';

export class AuthService {
  private static instance: AuthService;
  public userCredentials = new BehaviorSubject<AuthUser>({
    email: '',
    password: '',
    fromSignup: false,
  });

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  public updateUserCredentials(data: Partial<AuthUser>) {
    this.userCredentials.next({
      ...this.userCredentials.value,
      ...data,
    });
  }

  private async addUserToGroup(username: string, groupName: string) {
    try {
      return true;
    } catch (error) {
      console.error(`Error adding user to group ${groupName}:`, error);
      throw error;
    }
  }

  public async signUp(
    user: AuthUser,
    role: string = 'CLIENT'
  ) {
    try {
      const userAttributes: Record<string, string> = {
        name: user.name || `${user.email.split('@')[0]}`,
        email: user.email,
        'custom:role': role
      };

      Object.keys(userAttributes).forEach(key => {
        if (userAttributes[key] === undefined || userAttributes[key] === null) {
          delete userAttributes[key];
        }
      });

      const { isSignUpComplete, userId, nextStep } = await signUp({
        username: user.email,
        password: user.password,
        options: {
          userAttributes,
          autoSignIn: {
            enabled: false
          }
        },
      });

      try {
        await this.addUserToGroup(user.email, role);
      } catch (error) {
        console.error('Error adding user to group:', error);
      }
      
      return { isSignUpComplete, userId, nextStep };
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  }

  public async signIn(username: string, password: string) {
    try {
      const { isSignedIn, nextStep } = await signIn({ username, password });
      
      if (isSignedIn) {
        const currentUser = await getCurrentUser();
        return currentUser;
      }
      
      return { nextStep };
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  }

  public async setNewPassword(username: string, newPassword: string) {
    try {
      const result = await signIn({
        username,
        password: newPassword
      });
      return result;
    } catch (error) {
      console.error('Error setting new password:', error);
      throw error;
    }
  }

  public async signOut() {
    try {
      await signOut();
      this.userCredentials.next({
        email: '',
        password: '',
        fromSignup: false,
      });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  public async getCurrentUser() {
    try {
      const user = await getCurrentUser();
      const session = await fetchAuthSession();
      
      return {
        ...user,
        signInDetails: {
          loginId: user.signInDetails?.loginId,
          authFlowType: user.signInDetails?.authFlowType
        },
        session
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  public async getCurrentSession() {
    try {
      return await fetchAuthSession();
    } catch (error) {
      console.error('Error getting current session:', error);
      return null;
    }
  }

  public async confirmSignUp(username: string, code: string) {
    try {
      const result = await confirmSignUp({
        username,
        confirmationCode: code
      });
      return result;
    } catch (error) {
      console.error('Error confirming sign up:', error);
      throw error;
    }
  }

  public async forgotPassword(username: string) {
    try {
      await resetPassword({ username });
      return true;
    } catch (error) {
      console.error('Error initiating password reset:', error);
      throw error;
    }
  }

  public async forgotPasswordSubmit(
    username: string,
    code: string,
    newPassword: string
  ) {
    try {
      await confirmResetPassword({ 
        username, 
        confirmationCode: code, 
        newPassword 
      });
      return true;
    } catch (error) {
      console.error('Error confirming password reset:', error);
      throw error;
    }
  }
}

export const authService = AuthService.getInstance();
