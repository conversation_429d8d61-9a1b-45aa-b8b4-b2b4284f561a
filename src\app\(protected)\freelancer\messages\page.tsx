'use client';

import { useAuth } from '@/lib/auth/AuthContext';
import { MessagingLayout } from '@/components/layout/MessagingLayout';
import { Conversation, Message } from '@/components/messaging/types';
import { UserRole } from '@/types/enums';

export default function FreelancerMessagesPage() {
  const { user } = useAuth();
  
  const mockConversations: Conversation[] = [
    {
      id: '1',
      participants: [
        {
          id: 'client1',
          name: 'Client One',
          email: '<EMAIL>',
          role: UserRole.CLIENT,
          isOnline: true
        },
        {
          id: user?.username || 'freelancer1',
          name: user?.attributes?.name || 'Freelancer',
          email: user?.attributes?.email || '<EMAIL>',
          role: UserRole.FREELANCER,
          isOnline: true
        }
      ],
      lastMessage: {
        id: 'msg1',
        content: 'Hello, I have a project for you!',
        senderId: 'client1',
        timestamp: new Date(),
        status: 'delivered',
        type: 'text' as const
      },
      unreadCount: 1,
      updatedAt: new Date()
    },
  ];

  const handleSendMessage = async (conversationId: string, content: string) => {
    console.log(`Sending message to conversation ${conversationId}:`, content);
    return Promise.resolve();
  };

  const handleLoadMoreMessages = async (conversationId: string, before: Date) => {
    console.log(`Loading more messages for conversation ${conversationId} before`, before);
    return Promise.resolve([] as Message[]);
  };
  
  if (!user) {
    return (
      <div className="h-full flex items-center justify-center">
        <p>Loading user data...</p>
      </div>
    );
  }

  return (
    <div className="h-full flex-1 flex items-center justify-center bg-muted/50">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold mb-2">Your Messages</h2>
        <p className="text-muted-foreground mb-6">
          Select a conversation or start a new one to begin messaging.
        </p>
      </div>
    </div>
  );
}
