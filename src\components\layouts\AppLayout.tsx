import React from 'react';
import { Navbar, NavItem } from '@/components/layout/Navbar';
import { Container } from '@/components/layout/Container';
import { cn } from '@/lib/utils';

export interface AppLayoutProps {
  children: React.ReactNode;
  navigation?: NavItem[];
  className?: string;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  navigation,
  className,
  containerSize = 'lg',
  padding = true,
}) => {
  return (
    <div className="min-h-screen bg-background">
      <Navbar navigation={navigation} />
      
      <main className={cn('flex-1 py-8', className)}>
        <Container size={containerSize} padding={padding}>
          {children}
        </Container>
      </main>
    </div>
  );
};

// Auth Layout for login/signup pages
export interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  className?: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showLogo = true,
  className,
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-timberwolf-50 to-sage-100 flex items-center justify-center p-4 sm:p-8">
      <div className="w-full max-w-[420px] space-y-8">
        {showLogo && (
          <div className="text-center">
            <div className="mx-auto h-16 w-16 rounded-xl bg-primary flex items-center justify-center shadow-lg">
              <span className="text-primary-foreground font-bold text-xl">MV</span>
            </div>
            <h2 className="mt-4 text-2xl font-bold text-foreground">MyVillage</h2>
          </div>
        )}

        {(title || subtitle) && (
          <div className="text-center space-y-2">
            {title && (
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-sm sm:text-base text-muted-foreground">
                {subtitle}
              </p>
            )}
          </div>
        )}

        <div className={cn('bg-card rounded-xl shadow-md border border-border/50 p-6 sm:p-8 w-full', className)}>
          <div className="space-y-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export interface ErrorLayoutProps {
  children: React.ReactNode;
  code?: string;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

const ErrorLayout: React.FC<ErrorLayoutProps> = ({
  children,
  code,
  title,
  description,
  actions,
  className,
}) => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className={cn('w-full max-w-lg text-center space-y-8', className)}>
        {code && (
          <div className="text-6xl font-bold text-primary">
            {code}
          </div>
        )}
        
        {title && (
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            {title}
          </h1>
        )}
        
        {description && (
          <p className="text-lg text-muted-foreground">
            {description}
          </p>
        )}
        
        <div className="space-y-6">
          {children}
        </div>
        
        {actions && (
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export { AppLayout, AuthLayout, ErrorLayout };
