"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { Job, JobFilter, JobCategory, JobStatus } from "@/types/job";
import { jobService } from "@/api/jobService";
import Link from "next/link";
import { Button } from "@/components/ui";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";
import { Table } from "@/components/ui/Table";
import type { Column } from "@/types/components/Table";
import { Icon } from "@/components/ui";
import { Input } from "@/components/ui/Input";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { formatDistanceToNow } from "date-fns";

type JobWithProposalStatus = Omit<Job, "proposals"> & {
  hasSubmittedProposal?: boolean;
  myProposal?: Record<string, unknown> | null;
  proposals?: string; // JSON string or count
  proposalsCount?: number; // Number of proposals
  [key: string]:
    | string
    | number
    | boolean
    | Record<string, unknown>
    | null
    | undefined
    | JobCategory
    | JobStatus;
};

type JobFilters = {
  searchTerm: string;
  category: JobCategory | "";
  minBudget: string;
  maxBudget: string;
  isRemote: boolean;
};

const ITEMS_PER_PAGE = 5;

const FreelancerJobsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [jobs, setJobs] = useState<JobWithProposalStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<JobFilters>({
    searchTerm: "",
    category: "" as JobCategory | "",
    minBudget: "",
    maxBudget: "",
    isRemote: false,
  });

  useEffect(() => {
    if (
      !authLoading &&
      (!isAuthenticated || user?.attributes?.["custom:role"] !== "FREELANCER")
    ) {
      router.push("/login");
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchJobs = useCallback(async () => {
    if (!isAuthenticated || !user?.username) {
      return;
    }

    try {
      setIsLoading(true);
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;

      const filterParams: JobFilter = {
        status: "OPEN",
        category: filters.category || undefined,
        minBudget: filters.minBudget
          ? parseFloat(filters.minBudget)
          : undefined,
        maxBudget: filters.maxBudget
          ? parseFloat(filters.maxBudget)
          : undefined,
        isRemote: filters.isRemote || undefined,
        searchTerm: filters.searchTerm || undefined,
      };

      const response = await jobService.listJobs(filterParams);
      let allJobs = Array.isArray(response.items) ? response.items : [];

      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        allJobs = allJobs.filter(
          (job) =>
            job.title.toLowerCase().includes(searchLower) ||
            job.description.toLowerCase().includes(searchLower)
        );
      }

      const jobsWithProposalStatus = await Promise.all(
        allJobs.map(async (job) => {
          try {
            const hasSubmittedProposal = await jobService.hasSubmittedProposal(
              job.id,
              user.username
            );
            const plainJob = {
              ...job,
              client: job.client ? { ...job.client } : null,
              hasSubmittedProposal,
              myProposal: undefined,
              proposals: job.proposals ? JSON.stringify(job.proposals) : "[]",
              proposalsCount: job.proposals?.length || 0,
            } as unknown as JobWithProposalStatus;
            return plainJob;
          } catch (error) {
            console.error(
              `Error checking proposal status for job ${job.id}:`,
              error
            );
            return {
              ...job,
              hasSubmittedProposal: false,
              myProposal: undefined,
              proposals: "[]",
              proposalsCount: 0,
            } as unknown as JobWithProposalStatus;
          }
        })
      );

      const totalItems = jobsWithProposalStatus.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedJobs = jobsWithProposalStatus.slice(startIndex, endIndex);

      setJobs(paginatedJobs);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
      setJobs([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        router.replace("?page=1", { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.username, searchParams, router, filters]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.["custom:role"] === "FREELANCER") {
      fetchJobs();
    }
  }, [isAuthenticated, user, fetchJobs]);

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleFilterChange = (
    field: keyof JobFilters,
    value: string | boolean | JobCategory
  ) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (currentPage !== 1) {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1");
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleClearFilters = () => {
    setFilters({
      searchTerm: "",
      category: "" as JobCategory | "",
      minBudget: "",
      maxBudget: "",
      isRemote: false,
    });
    if (currentPage !== 1) {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1");
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleApply = (jobId: string) => {
    router.push(`/freelancer/jobs/${jobId}/apply`);
  };

  const columns: Column<JobWithProposalStatus>[] = [
    {
      header: "Job Title",
      accessor: "title",
      sortable: true,
      cell: (value: unknown, row: JobWithProposalStatus) => {
        const title = value as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-foreground">{title}</span>
            <span className="text-xs text-muted-foreground">
              {row.category?.replace("_", " ")}
            </span>
          </div>
        );
      },
    },
    {
      header: "Client",
      accessor: "clientId",
      cell: (_: unknown, row: JobWithProposalStatus) => {
        return (
          <span className="text-sm text-muted-foreground">
            {row.client?.name || "Client"}
          </span>
        );
      },
    },
    {
      header: "Posted",
      accessor: "createdAt",
      sortable: true,
      cell: (value: unknown) => {
        if (!value)
          return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date =
          typeof dateValue === "string" ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: "Budget",
      accessor: "budget",
      sortable: true,
      cell: (value: unknown) => {
        const budget = value as number | null | undefined;
        return (
          <span className="font-medium">
            ${budget?.toLocaleString() || "-"}
          </span>
        );
      },
    },
    {
      header: "Status",
      accessor: (row: JobWithProposalStatus) =>
        row.hasSubmittedProposal ? "Applied" : "Available",
      cell: (_: unknown, row: JobWithProposalStatus) => {
        const hasApplied = row.hasSubmittedProposal;
        return (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              hasApplied
                ? "bg-blue-100 text-blue-800"
                : "bg-green-100 text-green-800"
            }`}
          >
            {hasApplied ? "Applied" : "Available"}
          </span>
        );
      },
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_: unknown, row: JobWithProposalStatus) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/jobs/${row.id}`);
        };

        const handleApplyClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleApply(row.id);
        };

        return (
          <div className="flex items-center justify-end space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground"
              title="View Job"
            >
              <Icon name="Eye" size="sm" />
            </Button>
            {!row.hasSubmittedProposal && row.status === "OPEN" && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleApplyClick}
                className="h-8 w-8 text-muted-foreground hover:text-primary hover:bg-primary/10"
                title="Submit Proposal"
              >
                <Icon name="Send" size="sm" />
              </Button>
            )}
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const tablePagination = {
    enabled: totalItems > 0,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE) || 1,
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: "mt-4",
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Available Jobs</h1>
        <p className="text-muted-foreground">
          Browse and submit proposals for jobs that match your skills
        </p>
      </div>
      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Icon name="Filter" size="sm" />
          <span>Filters</span>
        </Button>
        <Button asChild>
          <Link href="/freelancer/proposals" className="flex items-center">
            <Icon name="Briefcase" size="sm" className="mr-2" />
            View My Proposals
          </Link>
        </Button>
      </div>
    </div>

    {showFilters && (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <label htmlFor="searchTerm" className="text-sm font-medium">
                Search
              </label>
              <Input
                id="searchTerm"
                placeholder="Search jobs..."
                value={filters.searchTerm}
                onChange={(e) =>
                  handleFilterChange("searchTerm", e.target.value)
                }
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">
                Category
              </label>
              <Select
                value={filters.category}
                onChange={(e) =>
                  handleFilterChange("category", e.target.value as JobCategory)
                }
                options={[
                  { value: "", label: "All Categories" },
                  { value: "WEB_DEVELOPMENT", label: "Web Development" },
                  { value: "MOBILE_DEVELOPMENT", label: "Mobile Development" },
                  { value: "DESIGN", label: "Design" },
                  { value: "WRITING", label: "Writing" },
                  { value: "MARKETING", label: "Marketing" },
                  { value: "ACCOUNTING", label: "Accounting" },
                  { value: "LEGAL", label: "Legal" },
                  { value: "OTHER", label: "Other" },
                ]}
                placeholder="Select a category"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="minBudget" className="text-sm font-medium">
                Min Budget ($)
              </label>
              <Input
                id="minBudget"
                placeholder="Min budget"
                value={filters.minBudget}
                onChange={(e) =>
                  handleFilterChange("minBudget", e.target.value)
                }
                type="number"
                min="0"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="maxBudget" className="text-sm font-medium">
                Max Budget ($)
              </label>
              <Input
                id="maxBudget"
                placeholder="Max budget"
                value={filters.maxBudget}
                onChange={(e) =>
                  handleFilterChange("maxBudget", e.target.value)
                }
                type="number"
                min={filters.minBudget || "0"}
              />
            </div>
            <div className="flex items-center space-x-2 pt-7">
              <Checkbox
                id="isRemote"
                checked={filters.isRemote}
                onChange={(e) =>
                  handleFilterChange("isRemote", e.target.checked)
                }
              />
              <label htmlFor="isRemote" className="text-sm font-medium">
                Remote Only
              </label>
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClearFilters}>
              Reset
            </Button>
            <Button onClick={() => setShowFilters(false)}>Apply Filters</Button>
          </div>
        </CardContent>
      </Card>
    )}

    <Card className="shadow-sm">
      <CardContent>
        <Table<JobWithProposalStatus>
          columns={columns}
          data={jobs}
          isLoading={isLoading}
          pagination={tablePagination}
          emptyState={{
            title: "No jobs available",
            description:
              "There are currently no jobs available. Check back later for new opportunities.",
            icon: "Briefcase",
            action: (
              <Button asChild size="sm">
                <Link href="/freelancer/proposals">
                  <Icon name="Briefcase" size="sm" className="mr-2" />
                  View My Proposals
                </Link>
              </Button>
            ),
          }}
        />
      </CardContent>
    </Card>
  </div>
  );
};

export default FreelancerJobsPage;
