import { gql } from '@apollo/client';

export const GET_JOB = gql`
  query GetJob($id: ID!) {
    getJob(id: $id) {
      id
      title
      description
      budget
      category
      deadline
      createdAt
      updatedAt
      clientId
    }
  }
`;

export const LIST_JOBS = gql`
  query ListJobs($filter: ModelJobFilterInput, $limit: Int, $nextToken: String) {
    listJobs(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        budget
        category
        deadline
        createdAt
        clientId
      }
      nextToken
    }
  }
`;

export const LIST_MY_JOBS = gql`
  query ListMyJobs($clientId: ID!, $status: String) {
    listMyJobs(clientId: $clientId, status: $status) {
      items {
        id
        title
        description
        budget
        category
        deadline
        createdAt
        proposals {
          items {
            id
            status
            freelancerId
            createdAt
          }
        }
      }
      nextToken
    }
  }
`;
