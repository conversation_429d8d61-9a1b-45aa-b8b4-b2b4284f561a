'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui/Icon';

export interface SidebarItem {
  name: string;
  href: string;
  icon?: React.ReactNode;
  badge?: string | number;
  children?: SidebarItem[];
}

export interface SidebarProps {
  items: SidebarItem[];
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  items,
  className,
  collapsible = true,
  defaultCollapsed = false,
  onCollapseChange
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // Sync collapsed state with parent
  useEffect(() => {
    setCollapsed(defaultCollapsed);
  }, [defaultCollapsed]);

  const handleToggleCollapse = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    onCollapseChange?.(newCollapsed);
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };



  const renderItem = (item: SidebarItem, level = 0) => {
    const active = isActive(item.href);
    const hasChildren = item.children && item.children.length > 0;
    const expanded = expandedItems[item.href] ?? active;

    return (
      <div key={item.href}>
        <Link
          href={item.href}
          className={cn(
            'flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors',
            level > 0 && 'ml-4',
            active
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
            collapsed && level === 0 && 'justify-center px-2'
          )}
          onClick={() => hasChildren && setExpandedItems(prev => ({ ...prev, [item.href]: !expanded }))}
        >
          <div className="flex items-center space-x-3">
            {item.icon && (
              <span className={cn('flex-shrink-0', collapsed && level === 0 && 'mx-auto')}>
                {item.icon}
              </span>
            )}
            {(!collapsed || level > 0) && (
              <span className="truncate">{item.name}</span>
            )}
          </div>
          
          {(!collapsed || level > 0) && (
            <div className="flex items-center space-x-2">
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
              {hasChildren && (
                <Icon 
                  name="ChevronRight" 
                  size="sm"
                  className={cn(
                    'transition-transform',
                    expanded && 'rotate-90'
                  )}
                />
              )}
            </div>
          )}
        </Link>
        
        {hasChildren && expanded && (!collapsed || level > 0) && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) => renderItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <aside
      className={cn(
        'h-full bg-background border-r border-border flex flex-col transition-all duration-300',
        collapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {collapsible && (
        <div className="flex items-center justify-between p-4">
          {!collapsed && (
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 rounded bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-xs">MV</span>
              </div>
              <span className="text-sm font-semibold text-foreground">MyVillage</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleToggleCollapse}
            className="h-8 w-8"
          >
            <Icon 
              name="ChevronsLeft" 
              size="sm"
              className={cn('transition-transform', collapsed ? 'rotate-0' : 'rotate-180')}
            />
          </Button>
        </div>
      )}

      <nav className="flex-1 space-y-1 p-4">
        {items.map((item) => renderItem(item))}
      </nav>
    </aside>
  );
};

export { Sidebar };
