'use client';

import React from 'react';
import { AppLayout } from '@/components/layouts';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Input,
  Textarea,
  Select,
  Checkbox,
  RadioGroup,
  Loading,
  Tabs,
  Pagination,
  Breadcrumb,
  Icon,
  Table
} from '@/components/ui';
import { iconSizes } from '@/types/ui';

// Sample data for the table demo
const tableData = [
  { id: 1, name: 'Website Redesign', status: 'active', priority: 'high', date: '2023-06-15', progress: 75 },
  { id: 2, name: 'Mobile App Development', status: 'active', priority: 'medium', date: '2023-06-20', progress: 30 },
  { id: 3, name: 'API Integration', status: 'completed', priority: 'high', date: '2023-05-28', progress: 100 },
  { id: 4, name: 'UI/UX Review', status: 'pending', priority: 'low', date: '2023-07-01', progress: 10 },
  { id: 5, name: 'Performance Optimization', status: 'active', priority: 'high', date: '2023-06-25', progress: 45 },
];

const DesignSystemDemo = () => {
  const iconSizesList = (Object.keys(iconSizes) as Array<keyof typeof iconSizes>);
  
  const tabItems = [
    {
      id: 'components',
      label: 'Components',
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Buttons</h3>
            <div className="flex flex-wrap gap-2">
              <Button>Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Table</h3>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Basic Table</h4>
                <Table
                  data={tableData}
                  columns={[
                    { header: 'ID', accessor: 'id' },
                    { header: 'Name', accessor: 'name' },
                    { header: 'Status', accessor: 'status' },
                    { header: 'Priority', accessor: 'priority' },
                    { header: 'Due Date', accessor: 'date' },
                  ]}
                  className="mb-8"
                />
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Table with Custom Cells & Actions</h4>
                <Table
                  data={tableData}
                  columns={[
                    // {
                    //   header: 'Task',
                    //   accessor: 'name',
                    //   cell: (value, row) => (
                    //     <div className="font-medium">
                    //       {value}
                    //       <div className="text-xs text-muted-foreground">ID: {row.id}</div>
                    //     </div>
                    //   ),
                    // },
                    // {
                    //   header: 'Status',
                    //   accessor: 'status',
                    //   cell: (value: string) => (
                    //     <Badge
                    //       variant={
                    //         value === 'active' ? 'default' :
                    //         value === 'completed' ? 'secondary' : 'outline'
                    //       }
                    //     >
                    //       {value}
                    //     </Badge>
                    //   ),
                    // },
                    // {
                    //   header: 'Progress',
                    //   accessor: 'progress',
                    //   cell: (value: string) => (
                    //     <div className="w-full bg-muted rounded-full h-2">
                    //       <div 
                    //         className="bg-primary h-2 rounded-full" 
                    //         style={{ width: `${value}%` }}
                    //       />
                    //     </div>
                    //   ),
                    // },
                    // {
                    //   header: 'Priority',
                    //   accessor: 'priority',
                    //   cell: (value: string) => (
                    //     <span className={`capitalize ${
                    //       value === 'high' ? 'text-red-500' :
                    //       value === 'medium' ? 'text-yellow-500' : 'text-green-500'
                    //     }`}>
                    //       {value}
                    //     </span>
                    //   ),
                    // },
                    // {
                    //   header: 'Actions',
                    //   accessor: 'id',
                    //   cell: (value: string) => (
                    //     <div className="flex space-x-2">
                    //       <Button variant="outline" size="sm">
                    //         <Icon name="Edit" className="h-4 w-4 mr-1" />
                    //         Edit
                    //       </Button>
                    //       <Button variant="outline" size="sm">
                    //         <Icon name="Trash2" className="h-4 w-4 mr-1" />
                    //         Delete
                    //       </Button>
                    //     </div>
                    //   ),
                    //   className: 'text-right',
                    // },
                  ]}
                  onRowClick={(row) => console.log('Row clicked:', row)}
                  className="mb-8"
                />
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Table with Empty State</h4>
                <Table
                  data={[]}
                  columns={[
                    { header: 'Name', accessor: 'name' },
                    { header: 'Status', accessor: 'status' },
                    { header: 'Date', accessor: 'date' },
                  ]}
                  emptyState={{
                    title: 'No items found',
                    description: 'There are no items to display at the moment.',
                    icon: 'Inbox',
                    action: (
                      <Button variant="outline">
                        <Icon name="Plus" className="h-4 w-4 mr-2" />
                        Add New Item
                      </Button>
                    ),
                  }}
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Badges</h3>
            <div className="flex flex-wrap gap-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Loading States</h3>
            <div className="flex items-center gap-4">
              <Loading variant="spinner" size="sm" />
              <Loading variant="dots" size="md" />
              <Loading variant="pulse" size="lg" />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Icons</h3>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Size Variants</h4>
                <div className="flex flex-wrap items-center gap-6 p-4 border rounded-lg">
                  {iconSizesList.map((size) => (
                    <div key={size} className="flex flex-col items-center gap-1">
                      <Icon name="Star" size={size} className="text-amber-400" />
                      <span className="text-xs text-muted-foreground">{size}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Common Icons</h4>
                <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-4 p-4 border rounded-lg">
                  {(['Home', 'Search', 'User', 'Settings', 'Bell', 'Mail', 'Calendar', 'Clock', 'Check', 'X', 'Plus', 'Minus', 'ChevronRight', 'ChevronDown', 'Star', 'Heart'] as const).map((icon) => (
                    <div key={icon} className="flex flex-col items-center gap-1">
                      <Icon name={icon} size="md" className="text-primary" />
                      <span className="text-xs text-muted-foreground text-center">{icon}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Usage with Buttons</h4>
                <div className="flex flex-wrap items-center gap-2">
                  <Button>
                    <Icon name="Plus" size="sm" className="mr-2" />
                    Add Item
                  </Button>
                  <Button variant="outline">
                    <Icon name="Search" size="sm" className="mr-2" />
                    Search
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Icon name="Settings" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Icon name="Bell" />
                    <span className="sr-only">Notifications</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'forms',
      label: 'Forms',
      content: (
        <div className="space-y-6 max-w-md">
          <div>
            <label className="block text-sm font-medium mb-2">Input</label>
            <Input placeholder="Enter text..." />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Textarea</label>
            <Textarea placeholder="Enter description..." rows={3} />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Select</label>
            <Select 
              options={[
                { value: '', label: 'Choose option...' },
                { value: 'option1', label: 'Option 1' },
                { value: 'option2', label: 'Option 2' },
              ]}
              placeholder="Choose option..."
            />
          </div>
          
          <div>
            <Checkbox label="Accept terms and conditions" />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Radio Group</label>
            <RadioGroup
              name="demo"
              options={[
                { value: 'option1', label: 'Option 1' },
                { value: 'option2', label: 'Option 2' },
              ]}
            />
          </div>
        </div>
      ),
    },
    {
      id: 'cards',
      label: 'Cards',
      content: (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Simple Card</CardTitle>
              <CardDescription>This is a basic card example</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content goes here. This demonstrates the basic card layout with header and content sections.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Stats Card</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-muted-foreground">+20% from last month</p>
            </CardContent>
          </Card>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Design System', href: '/design-system-demo' },
    { label: 'Demo', current: true },
  ];

  return (
    <AppLayout>
      <div className="space-y-8">
        <div>
          <Breadcrumb items={breadcrumbItems} />
          <h1 className="text-3xl font-bold tracking-tight mt-4">Design System Demo</h1>
          <p className="text-muted-foreground mt-2">
            Explore the components and design tokens of the MyVillage design system.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
            <CardDescription>Our earth-tone color scheme</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-timberwolf-500 rounded-lg"></div>
                <p className="text-sm font-medium">Timberwolf</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-sage-500 rounded-lg"></div>
                <p className="text-sm font-medium">Sage</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-fern-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Fern Green</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-hunter-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Hunter Green</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-brunswick-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Brunswick Green</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs items={tabItems} defaultTab="components" />

        <Card>
          <CardHeader>
            <CardTitle>Pagination Example</CardTitle>
          </CardHeader>
          <CardContent>
            <Pagination
              currentPage={3}
              totalPages={10}
              onPageChange={(page) => console.log('Page:', page)}
            />
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default DesignSystemDemo;
