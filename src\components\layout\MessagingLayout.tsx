'use client';

import { ReactNode, useEffect, useState, useMemo } from 'react';
import { MessagingDrawer } from '@/components/messaging';
import { useMessaging } from '@/contexts/MessagingContext';
import { MessagingUser, Conversation, Message } from '@/components/messaging/types';

interface MessagingLayoutProps {
  children: ReactNode;
  currentUser: MessagingUser;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onFileUpload?: (file: File) => Promise<string>;
}

export function MessagingLayout({
  children,
  currentUser,
  initialConversations = [],
  onSendMessage,
  onLoadMoreMessages,
  onFileUpload,
}: MessagingLayoutProps) {
  const { isDrawerOpen, closeMessagingDrawer } = useMessaging();
  const [conversations, setConversations] = useState<Conversation[]>([]);

  const memoizedInitialConversations = useMemo(() => initialConversations, [initialConversations]);

  useEffect(() => {
    setConversations(memoizedInitialConversations);
  }, [memoizedInitialConversations]);

  // Conversation selection will be handled by the MessagingDrawer component

  return (
    <>
      {children}
      
      <MessagingDrawer
        isOpen={isDrawerOpen}
        onClose={closeMessagingDrawer}
        currentUser={currentUser}
        conversations={conversations}
        onSendMessage={onSendMessage}
        onLoadMoreMessages={onLoadMoreMessages}
        onFileUpload={onFileUpload}
      />
    </>
  );
}
