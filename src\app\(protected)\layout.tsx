"use client";

import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { useEffect, useState } from "react";
import { Loading } from "@/components/ui";
import { MessagingProvider } from "@/contexts/MessagingContext";
import { MessagingLayout } from "@/components/layout/MessagingLayout";
import { MessagingUser } from "@/components/messaging/types";
import { UserRole } from "@/types/enums";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, loading, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!loading && !isAuthenticated && mounted) {
      router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
    }
  }, [isAuthenticated, loading, router, pathname, mounted]);

  if (!mounted || loading || (!isAuthenticated && mounted)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }
  
  const currentUser: MessagingUser = {
    id: user?.username || '',
    name: user?.attributes?.name || 'User',
    email: user?.attributes?.email || '',
    avatar: user?.attributes?.profilePhoto || '',
    role: (user?.attributes?.['custom:role'] as UserRole) || UserRole.CLIENT,
    isOnline: true,
  };

  const handleSendMessage = async (conversationId: string, content: string) => {
    console.log('Sending message:', { conversationId, content });
  };

  const handleLoadMoreMessages = async (conversationId: string, before: Date) => {
    console.log('Loading more messages:', { conversationId, before });
    return [];
  };

  const handleFileUpload = async (file: File) => {
    console.log('Uploading file:', file.name);
    return URL.createObjectURL(file);
  };

  return (
    <MessagingProvider
      currentUser={currentUser}
      initialConversations={[]}
      onSendMessage={handleSendMessage}
      onLoadMoreMessages={handleLoadMoreMessages}
      onFileUpload={handleFileUpload}
    >
      <MessagingLayout
        currentUser={currentUser}
        initialConversations={[]}
        onSendMessage={handleSendMessage}
        onLoadMoreMessages={handleLoadMoreMessages}
        onFileUpload={handleFileUpload}
      >
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </MessagingLayout>
    </MessagingProvider>
  );
}
